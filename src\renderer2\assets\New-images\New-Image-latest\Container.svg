<svg width="244" height="42" viewBox="0 0 244 42" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_78_1399)">
<rect x="1" y="1" width="242" height="40" rx="13.2597" fill="#23242A" fill-opacity="0.2"/>
<rect x="0.5" y="0.5" width="243" height="41" rx="13.7597" stroke="url(#paint0_linear_78_1399)" stroke-opacity="0.33"/>
</g>
<defs>
<filter id="filter0_i_78_1399" x="0" y="0" width="247" height="45" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="3" dy="3"/>
<feGaussianBlur stdDeviation="3.7"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_78_1399"/>
</filter>
<linearGradient id="paint0_linear_78_1399" x1="275.938" y1="66.0773" x2="240.242" y2="-43.9977" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#1A1B20"/>
</linearGradient>
</defs>
</svg>
