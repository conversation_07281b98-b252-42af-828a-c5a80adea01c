import { commomKeys, emojiRemoverRegex, formatToTwoDecimalPlaces, noIdGeneric, priceUnits, useBuyerSettingStore, useCreatePoStore, useGlobalStore } from "@bryzos/giss-ui-library";
import { clearLocal, fetchPrice, getLocal, setLocal } from "src/renderer2/helper";
import { useLeftPanelStore } from "../LeftPanelStore";
import styles from "../ListTab/ListTab.module.scss";
import { ReactComponent as DeleteIcon } from "../../../assets/New-images/New-Image-latest/delete-outlined.svg";
import { ReactComponent as ShareIcon } from "../../../assets/New-images/New-Image-latest/share-outlined.svg";
import { ReactComponent as EditIcon } from "../../../assets/New-images/New-Image-latest/pencil-outlined.svg";
import { useEffect, useRef, useState } from "react";
import clsx from "clsx";
import useDialogStore from "../../DialogPopup/DialogStore";
import useDeleteSearchProducts from "src/renderer2/hooks/useDeleteSearchProducts";
import { localStorageKeys, shareEmailTypes } from "src/renderer2/common";
import ShareEmailWindow from "../../ShareEmailWindow/ShareEmailWindow";
import dayjs from "dayjs";
import customParseFormat from "dayjs/plugin/customParseFormat";
import useGetDraftLines from "src/renderer2/hooks/useGetDraftLines";
import usePostCancelDraftPo from "src/renderer2/hooks/usePostCancelDraftPo";
import { useRightWindowStore } from "src/renderer2/pages/RightWindow/RightWindowStore";
import { Popover } from '@mui/material';
dayjs.extend(customParseFormat);

interface QuoteListProps {
    item: any;
    index: number;
    animatedItems: Set<string>;
}

const QuoteList = ({item, index, animatedItems}: QuoteListProps) => {
    const selectedQuote = useCreatePoStore(state => state.selectedQuote);
    const setSelectedQuote = useCreatePoStore(state => state.setSelectedQuote);
    const setCreatePoData = useCreatePoStore(state => state.setCreatePoData);
    const setShowLoader = useGlobalStore(state => state.setShowLoader);
    const productMapping = useGlobalStore(state => state.productMapping);
    const setIsCreatePoDirty = useCreatePoStore(state => state.setIsCreatePoDirty);
    const draftOrderListFromSocket = useCreatePoStore((state: any) => state.draftOrderListFromSocket);
    const setDraftOrderListFromSocket = useCreatePoStore((state: any) => state.setDraftOrderListFromSocket);
    const updatedDraftId = useCreatePoStore((state: any) => state.updatedDraftId);
    const setUpdatedDraftId = useCreatePoStore((state: any) => state.setUpdatedDraftId);
    const {mutateAsync: getDraftLines} = useGetDraftLines();
    const {mutateAsync: cancelDraftPo} = usePostCancelDraftPo();
    const [shareAnchorEl, setShareAnchorEl] = useState<HTMLButtonElement | null>(null);
    const { setShareEmailWindowProps, setLoadComponent, setShareEmailType } = useRightWindowStore();
    const { showCommonDialog, resetDialogStore } = useDialogStore();

    const isSharePopoverOpen = Boolean(shareAnchorEl && selectedQuote?.id === item.id);

    useEffect(()=>{
        if(item)
            if(item.id.includes(noIdGeneric) && !animatedItems.has(item.id)){
                setTimeout(()=>{
                    animatedItems.add(item.id);
                },3000);
            }
    },[item]);

    
    // useEffect(()=>{
    //     const getLocalQuote = getLocal(localStorageKeys.poQuoting, null);
    //     if(draftOrderListFromSocket?.length > 0 && selectedQuote?.id){
    //         const selectedQuoteFetchLatestData = draftOrderListFromSocket.find((item: any) => item.id === selectedQuote?.id);
    //         if(!selectedQuoteFetchLatestData) setDraftOrderListFromSocket([]);
    //         if(selectedQuoteFetchLatestData?.id && selectedQuoteFetchLatestData?.id === selectedQuote?.id){
    //             setDraftOrderListFromSocket([]);
    //             handleUpdateSelectedData(selectedQuoteFetchLatestData);
    //         }
    //     }
    // },[draftOrderListFromSocket])

    const handleUpdateSelectedData = async(selectedQuoteFetchLatestData: any) => {
        if(!selectedQuote.pricing_expired){
            showCommonDialog(null, 'This quote has been updated.', null, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]);
        }
        clearLocal(localStorageKeys.poQuoting);
        setUpdatedDraftId(null);
        await handleLoadQuoteData(selectedQuoteFetchLatestData);

    }

    const handleItemClick = async (item: any, index: number, e: React.MouseEvent) => {
        try{
            if(selectedQuote?.id === item.id){
                setSelectedQuote(null);
                return;
            }
            await handleLoadQuoteData(item);
        } catch(error){
            setShowLoader(false);
            console.log("error @>>>>>>>", error);
        }
    }

    const handleLoadQuoteData = async (item: any) => {
        setShowLoader(true);
        setIsCreatePoDirty(false);
        const draftLines = await getDraftLines(item.id);
        updateQuoteData(item, draftLines?.data);
        // setShowLoader(false);
    }

    const updateQuoteData = (quoteData: any, quoteLinesData: any) => {
        let createPoData: any = {
            id: quoteData.id,
            isEdit: !quoteData.pricing_expired,
            buyer_internal_po: quoteData.buyer_internal_po,
            delivery_date: quoteData.delivery_date,
            shipping_details: quoteData.shipping_details,
            order_type: quoteData.order_type,
            buyer_po_price: quoteData.buyer_po_price,
            created_date: quoteData.created_date,
            deposit_amount: quoteData.deposit_amount,
            freight_amount: quoteData.freight_amount,
            last_price_updated_date: quoteData.last_price_updated_date,
            payment_method: quoteData.payment_method,
            pricing_expired: quoteData.pricing_expired,
            sales_tax: quoteData.sales_tax,
            source: quoteData.source,
            subscription: quoteData.subscription,
            total_weight: quoteData.total_weight
        }
        // const headerInfo = {
        // }
        // createPoData.headerInfo = headerInfo;
        createPoData.cart_items = quoteLinesData?.map((line: any) => {
            return {
                ...line,
                descriptionObj: productMapping[line.product_id]
            }
        });
        setSelectedQuote(createPoData);
        setCreatePoData(createPoData);
    }

    const handleEditClick = (item: any, e: React.MouseEvent) => {
        e.stopPropagation();
        if(selectedQuote?.id === item.id){
            const updateSelectedQuote = {...selectedQuote, isEdit: true};
            setSelectedQuote(updateSelectedQuote);
            setCreatePoData(updateSelectedQuote);
        }
    }

    const handleDeleteClick = async (e: React.MouseEvent, item: any) => {
        e.stopPropagation();
        console.log("delete icon clicked @>>>>>>>", e, item);
        try{
            const payload = {
                id: item.id
            }
            setSelectedQuote(null);
            const response = await cancelDraftPo(payload);
            if(response?.data){
                setSelectedQuote(null);
            }
        } catch(error){
            console.log("error @>>>>>>>", error);
            await handleLoadQuoteData(item);
        }
    }
    const handleExportToPDF = (e: React.MouseEvent) => {
        e.stopPropagation();
        console.log("handleExportToPDF @>>>>>>>")
        setShareAnchorEl(null);
    }

    const handleExportToExcel = (e: React.MouseEvent) => {
        e.stopPropagation();
        console.log("handleExportToExcel @>>>>>>>")
        setShareAnchorEl(null);
    }

    const handleSharePriceClick = async (e: React.MouseEvent<HTMLButtonElement>, item: any) => {
        e.stopPropagation();
        setShareAnchorEl(e.currentTarget);
        if(selectedQuote?.id === item.id) return;
        await handleLoadQuoteData(item);
    }

    const handleCloseSharePopover = (e) => {
        e.stopPropagation();
        setShareAnchorEl(null);
    }

    const handleSharePrice = async (e: React.MouseEvent, item: any) => {
        e.stopPropagation();
        console.log("handleSharePrice @>>>>>>>", item)
        setShareEmailWindowProps({ isSharePrice: true });
        // setLoadComponent(<ShareEmailWindow />)
        setShareEmailType(shareEmailTypes.shareQuote);
        setShareAnchorEl(null);
    }
    
    let animate = false;
    if(item.id.includes(noIdGeneric) && !animatedItems.has(item.id)) {
        animate = true;
    }

    return (
        <div
            key={index}
            className={clsx(styles.searchItemContainer, (selectedQuote?.id === item.id) && styles.selectedSearchItem)}
            onClick={(e) => { handleItemClick(item, index, e); }}>
            <div className={styles.searchTitle}>
                <span className={clsx(styles.searchTitleText, {
                    [styles.initialPositionForAnimation]: animate,
                    [styles.slideInAnimation1]: animate
                })}>
                    {item.buyer_internal_po}
                    {/* {((selectedQuote?.id !== item.id) || (selectedQuote?.id === item.id && !selectedQuote?.isEdit)) &&
                    <span className={styles.editIcon} onClick={(e) => handleEditClick(item, e)}><EditIcon /></span>
                } */}
                </span>
                <span className={styles.itemCount}>${formatToTwoDecimalPlaces(item.buyer_po_price)}</span>
                <div className={styles.iconContainer}>
                    {/* <span className={styles.shareIcon} onClick={(e) => console.log("share icon clicked ", e, item)}></span> */}
                    <div className={styles.exportContainer}>
                        <button
                            className={clsx(styles.selectedProductHeaderButton)}
                            onClick={(e) => handleSharePriceClick(e, item)}
                            
                        >
                            <ShareIcon />
                        </button>
                        <Popover
                            open={isSharePopoverOpen}
                            anchorEl={shareAnchorEl}
                            onClose={handleCloseSharePopover}
                            anchorOrigin={{
                                vertical: 'top',
                                horizontal: 'center',
                            }}
                            transformOrigin={{
                                vertical: 'bottom',
                                horizontal: 86,
                            }}
                            classes={{
                                paper: styles.exportDropdownMenu,
                            }}
                        >
                            <div>
                                <button
                                    className={styles.exportOption}
                                    onClick={(e) => handleSharePrice(e, item)}
                                >
                                    Share
                                </button>
                                <button
                                    className={styles.exportOption}
                                    onClick={handleExportToPDF}
                                >
                                    Export to PDF
                                </button>
                                <button
                                    className={styles.exportOption}
                                    onClick={handleExportToExcel}
                                >
                                    Export to Excel
                                </button>
                            </div>
                        </Popover>
                    </div>
                    <span className={styles.deleteIcon} onClick={(e) => handleDeleteClick(e, item)}><DeleteIcon /></span>
                </div>
            </div>
            <div className={styles.searchDetails}>
                <div className={styles.positionRelative}>
                    <span className={clsx({
                        [styles.initialPositionForAnimation]: animate,
                        [styles.slideInAnimation2]: animate
                    })}>{item.shipping_details?.city} {item.shipping_details?.state_code}
                    </span>
                </div>
                <div className={styles.positionRelative}>
                    <span className={clsx({
                        [styles.initialPositionForAnimation]: animate,
                        [styles.slideInAnimation3]: animate
                    })}>{item.total_weight || 0} LBS</span>
                </div>
                {/* {item?.products?.length > 0 ?
                Array.from(new Set(
                    item?.products
                        .map((obj: any) => productMapping[obj.product_id]?.Key2 ?? '')
                )).join(', ')
            : '-'} */}
                {/* {orderSizeData ? <span>Based Upon {Number(orderSizeData?.min_weight) === Number(orderSizeList[orderSizeList.length - 1].min_weight) ? Number(orderSizeData?.min_weight).toLocaleString() + '+' : `${Number(orderSizeData?.min_weight).toLocaleString()} to ${Number(orderSizeData?.max_weight).toLocaleString()}`} LBS</span> : '-'}<br /> */}
                <div className={styles.positionRelative}>
                    <span className={clsx({
                        [styles.initialPositionForAnimation]: animate,
                        [styles.slideInAnimation4]: animate
                    })}>{item?.created_date ? item.created_date : '-'}</span>
                </div>
            </div>

        </div>
    )
}

export default QuoteList